{"name": "twcg-backend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode runtime", "build": "tsc -b && vite build", "build:develop": "vite build --mode develop", "build:uat": "vite build --mode uat", "lint": "eslint .", "preview": "vite preview", "postinstall": "node ./postinstall.js", "translate": "node ./translate.cjs", "prepare": "husky"}, "lint-staged": {"*.{js,ts,tsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.74.3", "@tinymce/miniature": "^6.0.0", "@tinymce/tinymce-react": "^6.2.0", "antd": "^5.24.7", "axios": "^1.8.4", "clsx": "^2.1.1", "dayjs": "^1.11.13", "fs-extra": "^11.3.0", "i18next": "^25.0.0", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.4.1", "react-router": "^7.5.1", "react-router-dom": "^7.5.1", "sass": "^1.87.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.4", "tinymce": "^7.9.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@svgr/plugin-svgo": "^8.1.0", "@tailwindcss/postcss": "^4.1.4", "@tanstack/eslint-plugin-query": "^5.73.3", "@types/qrcode": "^1.5.5", "@types/react": "^18.2.78", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-auto-import": "^0.1.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^15.15.0", "husky": "^9.1.7", "lint-staged": "^15.5.2", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "postcss-nesting": "^13.0.1", "prettier": "3.5.3", "prettier-plugin-sort-json": "^4.1.1", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "unplugin-auto-import": "^19.1.2", "vite": "^6.2.0", "vite-plugin-pages": "^0.33.0", "vite-plugin-svgr": "^4.3.0"}}