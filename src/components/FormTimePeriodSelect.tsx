import { FormInstance } from 'antd/es/form';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

import RDatePicker from '@/components/RDatePicker';
import RForm from '@/components/RForm';
import RSelect from '@/components/RSelect';
import { PublishType, PublishTypeEnum } from '@/types/common';
const { RangePicker } = RDatePicker;

type FormTimePeriodSelectProps = {
  form: FormInstance;
  value?: PublishType;
  onChange?: (value: PublishType) => void;
};

const FormTimePeriodSelect = ({ form }: FormTimePeriodSelectProps) => {
  const { t } = useTranslation();

  const timeTypeOptions = [
    {
      label: t('components_formTimePeriodSelect_publishType_permanent'),
      value: PublishTypeEnum.PERMANENT
    },
    {
      label: t('components_formTimePeriodSelect_publishType_fixedTime'),
      value: PublishTypeEnum.FIXED_TIME
    },
    {
      label: t('components_formTimePeriodSelect_publishType_timeRange'),
      value: PublishTypeEnum.TIME_RANGE
    }
  ];

  const publishTypeValue = RForm.useWatch('publishType', form);
  const timeRangeValue = RForm.useWatch('timeRange', form);

  useEffect(() => {
    if (publishTypeValue === PublishTypeEnum.PERMANENT) {
      form.setFieldsValue({ startTime: null, endTime: null });
    } else if (publishTypeValue === PublishTypeEnum.FIXED_TIME) {
      form.setFieldsValue({ endTime: null });
    }
  }, [publishTypeValue, form]);

  useEffect(() => {
    if (publishTypeValue === PublishTypeEnum.TIME_RANGE) {
      form.setFieldsValue({ startTime: timeRangeValue?.[0], endTime: timeRangeValue?.[1] });
    }
  }, [publishTypeValue, form, timeRangeValue]);

  return (
    <div>
      <div className="grid items-end grid-cols-2 gap-x-4">
        <RForm.Item
          name="publishType"
          label={t('components_formTimePeriodSelect_publishType')}
          initialValue={PublishTypeEnum.PERMANENT}
        >
          <RSelect options={timeTypeOptions} />
        </RForm.Item>
        {publishTypeValue !== PublishTypeEnum.TIME_RANGE && (
          <RForm.Item
            name="startTime"
            label={null}
            rules={[
              {
                required: publishTypeValue === PublishTypeEnum.FIXED_TIME,
                message: `${t('placeholder_input')}${t('common_startTime')}`
              }
            ]}
            getValueProps={(value) => ({ value: value && dayjs(Number(value)) })}
            normalize={(value) => value && `${dayjs(value).valueOf()}`}
          >
            <RDatePicker
              showTime
              disabled={publishTypeValue === PublishTypeEnum.PERMANENT}
            ></RDatePicker>
          </RForm.Item>
        )}
      </div>
      {publishTypeValue === PublishTypeEnum.TIME_RANGE && (
        <>
          <RForm.Item
            name="timeRange"
            rules={[
              {
                required: publishTypeValue === PublishTypeEnum.TIME_RANGE,
                message: `${t('placeholder_input')}${t('common_startTime')}`
              }
            ]}
            getValueProps={(value) => ({
              value: value && [dayjs(Number(value[0])), dayjs(Number(value[1]))]
            })}
            normalize={(value) => value && [value[0].valueOf(), value[1].valueOf()]}
          >
            <RangePicker showTime />
          </RForm.Item>
        </>
      )}
      <RForm.Item name="startTime" label={null} noStyle initialValue={null}></RForm.Item>
      <RForm.Item name="endTime" label={null} noStyle initialValue={null}></RForm.Item>
    </div>
  );
};

export default FormTimePeriodSelect;
