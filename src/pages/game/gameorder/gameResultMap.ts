import { GameOrderResult } from '@/enums/game';
type GameResultConfig = {
  label: string;
  color: string;
  textColor: string;
};
type GameResultMap = {
  [key in GameOrderResult]: GameResultConfig;
};

export const GAME_RESULT_MAP: Partial<GameResultMap> = {
  [GameOrderResult.Win]: {
    label: 'common_win',
    color: 'var(--tag-bg-enabled)',
    textColor: 'var(--color-success)'
  },
  [GameOrderResult.Lose]: {
    label: 'common_lose',
    color: 'var(--tag-bg-disabled)',
    textColor: 'var(--color-warning)'
  },
  [GameOrderResult.Draw]: {
    label: 'common_draw',
    color: '#FF831729',
    textColor: '#FF8317'
  },
  [GameOrderResult.Cancel]: {
    label: 'common_cancel',
    color: 'var(--tag-bg-disabled)',
    textColor: 'var(--color-warning)'
  },
  [GameOrderResult.Else]: {
    label: 'common_else',
    color: 'var(--tag-bg-disabled)',
    textColor: 'var(--color-warning)'
  }
};
