import './topupSetting.scss';

import { useTranslation } from 'react-i18next';

import { RTabs } from '@/components/RTabs';

import BasicSetting from './basicSetting';
import ChannelSetting from './channelSetting';
import PopularSettings from './popularSettings';
import SupplySetting from './supplySetting';
export default function TopUpSetting() {
  const { t } = useTranslation();
  const [activeTabKey, setActiveTabKey] = useState('chanel');
  const tabItems = [
    {
      key: 'chanel',
      label: t('pages_transaction_topupsetting_chanel'),
      children: <ChannelSetting />
    },
    {
      key: 'supply',
      label: t('pages_transaction_topupsetting_supply'),
      children: <SupplySetting />
    },
    {
      key: 'basic',
      label: t('pages_transaction_topupsetting_basic'),
      children: <BasicSetting />
    },
    {
      key: 'popular',
      label: t('pages_transaction_topupsetting_popular'),
      children: <PopularSettings />
    }
  ];
  return (
    <>
      <div className="topupSetting shadow-content">
        <RTabs
          activeKey={activeTabKey}
          onChange={setActiveTabKey}
          items={tabItems}
          indicator={{ size: 100, align: 'center' }}
          tabBarGutter={40}
          className="custom-tabs"
          rootClassName="ant-tabs-tab"
          destroyInactiveTabPane
        />
      </div>
    </>
  );
}
