import { useQuery } from '@tanstack/react-query';

import type { IResponse, IResponseDataPageStatic } from '@/api/services';
import {
  getTopupCategory,
  getTopupChannel,
  getTopupSupplier,
  type TopupOrderHistoryStatic
} from '@/api/topup';
import { getTopupIncompletedOrder } from '@/api/topup';
import type { TopupIncompletedOrder } from '@/types/topup';

import { topupKeys } from './topup';

export const useTopupSupplier = () => {
  return useQuery({
    queryKey: topupKeys.supplier.lists(),
    queryFn: getTopupSupplier
  });
};

export const useTopupCategory = () => {
  return useQuery({
    queryKey: topupKeys.category.lists(),
    queryFn: getTopupCategory
  });
};

export const useTopupChannel = (categoryKey: string | null) => {
  return useQuery({
    queryKey: topupKeys.channel.list(categoryKey),
    queryFn: () => getTopupChannel({ categoryKey }),
    enabled: !!categoryKey
  });
};

export const useTopupAllChannels = () => {
  return useQuery({
    queryKey: topupKeys.channel.list(null),
    queryFn: () => getTopupChannel({ categoryKey: null })
  });
};

export const useTopupIncompleteOrders = (params: {
  page: number;
  limit: number;
  [key: string]: unknown;
}) => {
  return useQuery<
    IResponse<IResponseDataPageStatic<TopupIncompletedOrder, TopupOrderHistoryStatic>>
  >({
    queryKey: topupKeys.order.incomplete.list(params),
    queryFn: () => getTopupIncompletedOrder(params)
  });
};
