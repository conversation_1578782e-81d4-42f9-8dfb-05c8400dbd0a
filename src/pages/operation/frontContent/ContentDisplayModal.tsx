import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import LanguageSelectTab from '@/components/LanguageSelectTab';
import RModal from '@/components/RModal';
import useFrontendLanguage from '@/hooks/useFrontendLanguage';
import { FrontContent } from '@/types/operation';

type ContentDisplayModalProps = {
  open: boolean;
  onClose: () => void;
  content?: FrontContent;
};

const ContentDisplayModal = ({ open, onClose, content }: ContentDisplayModalProps) => {
  const { t } = useTranslation();
  const { defaultFrontendLanguage } = useFrontendLanguage();
  const [activeLanguage, setActiveLanguage] = useState('');

  // Get available languages from content mapping
  const availableLanguages = useMemo(
    () => (content ? Object.keys(content.contentMapping) : []),
    [content]
  );

  // Set default active language when modal opens
  const handleLanguageChange = (language: string) => {
    setActiveLanguage(language);
  };

  // Get content for active language or default
  const getDisplayContent = () => {
    if (!content) return { title: '', content: '' };

    const targetLanguage = activeLanguage || defaultFrontendLanguage;
    return content.contentMapping[targetLanguage] || Object.values(content.contentMapping)[0];
  };

  const displayContent = getDisplayContent();

  // Initialize active language when content changes
  useEffect(() => {
    if (content && availableLanguages.length > 0) {
      const initialLanguage = availableLanguages.includes(defaultFrontendLanguage)
        ? defaultFrontendLanguage
        : availableLanguages[0];
      setActiveLanguage(initialLanguage);
    }
  }, [content, defaultFrontendLanguage, availableLanguages]);

  return (
    <RModal
      title={t('pages_frontContent_content')}
      open={open}
      onCancel={onClose}
      width={800}
      okButtonProps={{ show: false }}
      cancelButtonProps={{ text: t('common_close'), show: true }}
    >
      {content && (
        <div className="flex flex-col gap-4">
          {/* Language Selection */}
          {availableLanguages.length && (
            <LanguageSelectTab
              languageList={availableLanguages}
              onChange={() => {}} // Read-only, no need to change the list
              activeLanguage={activeLanguage}
              setActiveLanguage={handleLanguageChange}
              isReadOnly={true}
            />
          )}

          {/* Content Display */}
          <div className="space-y-4">
            {/* Title */}
            <div>
              <p className="text-gray-400 mb-2">{t('pages_frontContent_title')}</p>
              <div className="">{displayContent.title}</div>
            </div>

            {/* Content */}
            <div>
              <p className="text-gray-400 mb-2">{t('pages_frontContent_content')}</p>
              <div className="" dangerouslySetInnerHTML={{ __html: displayContent.content }} />
            </div>
          </div>
        </div>
      )}
    </RModal>
  );
};

export default ContentDisplayModal;
