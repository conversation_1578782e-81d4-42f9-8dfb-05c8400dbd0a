import { useMutation, useQueryClient } from '@tanstack/react-query';

import { createAnnouncement, updateAnnouncement } from '@/api/operation';
import FormModal from '@/components/FormModal';
import FormTimePeriodSelect from '@/components/FormTimePeriodSelect';
import HTMLEditor from '@/components/HTMLEditor';
import LanguageSelectTab from '@/components/LanguageSelectTab';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSwitch from '@/components/RSwitch';
import { Announcement } from '@/types/operation';

type AnnouncementModalProps = {
  open: boolean;
  onClose: () => void;
  initialValues?: Partial<Announcement>;
};

type FormValue = Pick<
  Announcement,
  'id' | 'contentMapping' | 'order' | 'status' | 'publishType' | 'startTime' | 'endTime'
> & {
  timeRange: [number, number];
};

const AnnouncementModal = ({ open, onClose, initialValues }: AnnouncementModalProps) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<FormValue>();
  const [languageList, setLanguageList] = useState<string[]>([]);
  const [activeLanguage, setActiveLanguage] = useState('');

  const isEdit = !!initialValues?.id;

  const queryClient = useQueryClient();

  const handleClose = () => {
    setLanguageList([]);
    setActiveLanguage('');
    onClose();
  };

  const onSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ['announcement'] });
    handleClose();
  };

  const { mutate: createAnnouncementMutation, isPending } = useMutation({
    mutationFn: createAnnouncement,
    onSuccess
  });

  const { mutate: updateAnnouncementMutation, isPending: isUpdatePending } = useMutation({
    mutationFn: updateAnnouncement,
    onSuccess
  });

  const handleSubmit = (values: FormValue) => {
    if (isEdit) {
      updateAnnouncementMutation({ ...values, id: Number(initialValues.id) });
    } else {
      createAnnouncementMutation({ ...values, order: 1 });
    }
  };

  const handleChangeLanguage = (value: string) => {
    setActiveLanguage(value);
  };

  useEffect(() => {
    if (initialValues) {
      setLanguageList(Object.keys(initialValues.contentMapping || {}));
      const timeout = setTimeout(() => {
        form.setFieldsValue({ ...initialValues });
        if (initialValues.endTime && initialValues.startTime) {
          form.setFieldsValue({
            timeRange: [initialValues.startTime, initialValues.endTime]
          });
        }
      }, 100);
      return () => clearTimeout(timeout);
    }
  }, [initialValues, form]);

  const isLoading = isPending || isUpdatePending;

  return (
    <FormModal<FormValue>
      title={
        isEdit
          ? t('common_edit_name', { name: t('pages_announcement') })
          : t('common_add_name', { name: t('pages_announcement') })
      }
      form={form}
      open={open}
      onClose={handleClose}
      onSubmit={handleSubmit}
      isLoading={isLoading}
      initialValues={initialValues}
      formProps={{
        onFinishFailed: (errorInfo) => {
          const contentMappingError = errorInfo.errorFields.find(
            (field) => field.name[0] === 'contentMapping' && field.name[1] !== activeLanguage
          );

          if (contentMappingError) {
            setActiveLanguage(contentMappingError.name[1] as string);
          }
        }
      }}
    >
      <div className="grid grid-cols-2 gap-x-15">
        <RForm.Item name="id" noStyle></RForm.Item>
        <FormTimePeriodSelect form={form} />
        <RForm.Item
          label={t('common_status')}
          name="status"
          valuePropName="checked"
          initialValue={1}
        >
          <RSwitch />
        </RForm.Item>
      </div>
      <LanguageSelectTab
        languageList={languageList}
        onChange={setLanguageList}
        activeLanguage={activeLanguage}
        setActiveLanguage={handleChangeLanguage}
      />
      {languageList.map((language) => {
        return (
          <div key={language} style={{ display: activeLanguage === language ? 'block' : 'none' }}>
            <RForm.Item
              name={['contentMapping', language, 'title']}
              label={t('pages_announcement_title')}
              rules={[{ required: true }]}
              className="!mt-4"
            >
              <RInput />
            </RForm.Item>
            <RForm.Item
              name={['contentMapping', language, 'content']}
              label={t('pages_announcement_content')}
              rules={[{ required: true }]}
            >
              <HTMLEditor />
            </RForm.Item>
          </div>
        );
      })}
    </FormModal>
  );
};

export default AnnouncementModal;
