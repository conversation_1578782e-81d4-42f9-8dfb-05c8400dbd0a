import { Button, Layout, Menu } from 'antd';
import { Outlet, useLocation } from 'react-router-dom';

import MenuFoldIcon from '@/assets/img/icon/menufold.svg?react';
import MenuUnfoldIcon from '@/assets/img/icon/menuunfold.svg?react';
import LogoBlue from '@/assets/img/logo-blue.svg';
import SwitchLanguage from '@/components/SwitchLanguage';
import UserInfoItem from '@/components/UserInfoItem';
import {
  getDefaultOpenKeys,
  getDefaultSelectedKey,
  useMenuClick,
  useMenuItems
} from '@/hooks/useSidebarItem';
const { Header, Sider, Content } = Layout;
import Bookmark from '@/components/Bookmark';
import SearchInput from '@/components/SearchInput';
import useRoute from '@/hooks/useRoute';

const CurrentRouteName = () => {
  const { currentRouteName } = useRoute();

  return <div className="px-4 py-4.5 text-text text-[14px] font-bold">{currentRouteName}</div>;
};

const BaseLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();
  const [selectedKey, setSelectedKey] = useState(getDefaultSelectedKey(location.pathname));
  const [openKeys, setOpenKeys] = useState<string[]>(getDefaultOpenKeys(location.pathname));
  // 從獨立配置獲取菜單項
  const originalItems = useMenuItems();
  const [menuItems, setMenuItems] = useState(originalItems);
  // 獲取菜單點擊處理函數
  const handleMenuClick = useMenuClick();

  // 同步路由變化到 selected 狀態
  useEffect(() => {
    const newSelectedKey = getDefaultSelectedKey(location.pathname);
    setSelectedKey(newSelectedKey);

    const newOpenKeys = getDefaultOpenKeys(location.pathname);
    setOpenKeys(newOpenKeys);
  }, [location.pathname]);

  const onMenuClick = ({ key }: { key: string }) => {
    handleMenuClick(key);
  };

  const handleSearch = (value: string) => {
    if (!value.trim()) {
      setMenuItems(originalItems);

      const allParentKeys = originalItems
        .filter((item): item is { key: string } => item?.key != null)
        .map((item) => item.key);
      setOpenKeys(allParentKeys);
      return;
    }

    const searchValue = value.toLowerCase();
    const matchedParentKeys: string[] = [];

    // 檢查標籤是否匹配搜索值
    const isLabelMatched = (label: React.ReactNode) => {
      if (!label) return false;
      return label.toString().toLowerCase().includes(searchValue);
    };

    const newMenuItems = originalItems
      .map((item) => {
        if (!item || !('key' in item) || !item.key) return null;

        const isParentMatched = 'label' in item && isLabelMatched(item.label);
        if ('children' in item && item.children) {
          const matchedChildren = item.children.filter((child) => {
            if (!child || !('key' in child) || !('label' in child)) return false;
            return isLabelMatched(child.label);
          });

          if (isParentMatched || matchedChildren.length > 0) {
            matchedParentKeys.push(item.key.toString());
            return {
              ...item,
              children: isParentMatched ? item.children : matchedChildren
            };
          }
        } else if (isParentMatched) {
          return item;
        }
        return null;
      })
      .filter(Boolean);

    setMenuItems(newMenuItems);
    // 設置匹配項的父菜單為展開狀態
    setOpenKeys(matchedParentKeys);
  };

  return (
    <Layout className="h-screen">
      <Sider trigger={null} collapsible collapsed={collapsed} theme="light" width={240}>
        <div className="demo-logo-vertical" />
        <div className="flex justify-center items-center space-x-[15px] h-[62px] py-[15px]">
          <div className="w-[32px]">
            <img src={LogoBlue} className="h-full cursor-pointer" alt="logo" />
          </div>
          {!collapsed && <div className="text-xl">HF OPS Console</div>}
        </div>
        {!collapsed && (
          <div className="w-[220px] mx-auto">
            <SearchInput onSearch={handleSearch} />
          </div>
        )}
        <Menu
          // theme="light"
          mode="inline"
          className="custom-menu"
          selectedKeys={[selectedKey]}
          openKeys={openKeys}
          onOpenChange={setOpenKeys}
          onClick={onMenuClick}
          items={menuItems}
        />
      </Sider>
      <Layout>
        <Header
          className="flex justify-between items-center"
          style={{ padding: 0, background: 'var(--color-header-bg)', height: '60px' }}
        >
          <Button
            type="text"
            icon={
              collapsed ? (
                <MenuUnfoldIcon className="!text-text-secondary" height={20} width={20} />
              ) : (
                <MenuFoldIcon className="!text-text-secondary" height={20} width={20} />
              )
            }
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64
            }}
          />
          <div className="flex justify-center items-center mr-7 space-x-5 h-full">
            <UserInfoItem />
            <SwitchLanguage />
          </div>
        </Header>
        <Content className="flex overflow-auto flex-col pb-4 h-full">
          <Bookmark />
          <div id="base-layout-header" className="w-full bg-white shadow-content z-1">
            <CurrentRouteName />
          </div>
          <div className="bg-bg-primary">
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default BaseLayout;
