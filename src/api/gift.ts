import {
  CompletedGiftOrder,
  CompletedGiftOrderSearchParams,
  GiftOrderSearchParams, GiftSetting, GiftStatus,
  GiftStatusFilter,
  IncompleteGiftOrder,
} from '@/types/gift';

import apiRequest, { IResponseDataPage } from './services';

export const getGiftSetting = () => {
  return apiRequest().get<GiftSetting>('/system/gift/list/setting');
};

// Get gift status options
export const getGiftStatus = (giftStatusFilter: GiftStatusFilter) => {
  return apiRequest().get<GiftStatus[]>('/system/gift/status', {
    params: {
      filter: giftStatusFilter
    }
  });
};

type GiftSettingFormValue = {
  receiveGiftVipLevel: number;
  giveGiftVipLevel: number;
  receiveGiftTimeLimit: number;
  receiveGiftFrozenRemainTimeLimit: number;
  vipSetting: {
    id: number;
    reserveAmount: number;
    minGiftAmount: number;
    giftTimes: number;
    giftQuotas: number;
    giftFeeRate: number;
  }[];
};

export const updateGiftSetting = (params: GiftSettingFormValue) => {
  return apiRequest().put('/system/gift/edit/setting', params);
};

// Get incomplete gift orders
export const getIncompleteGiftOrders = async (params: GiftOrderSearchParams) => {
  const data = {
    ...params
  };

  // Convert time parameters to match API expectations
  if (data.timeStart) {
    data.timeStart = Number(data.timeStart);
  }
  if (data.timeEnd) {
    data.timeEnd = Number(data.timeEnd);
  }

  return apiRequest().get<IResponseDataPage<IncompleteGiftOrder>>('/system/gift/incompleted', {
    params: data
  });
};

// Get completed gift orders
export const getCompletedGiftOrders = async (params: CompletedGiftOrderSearchParams) => {
  const data = {
    ...params
  };

  // Convert time parameters to match API expectations
  if (data.timeStart) {
    data.timeStart = Number(data.timeStart);
  }
  if (data.timeEnd) {
    data.timeEnd = Number(data.timeEnd);
  }

  return apiRequest().get<IResponseDataPage<CompletedGiftOrder>>('/system/gift/completed', {
    params: data
  });
};

export const triggerExportCompletedGiftOrders = async (params: Omit<CompletedGiftOrderSearchParams, 'page' | 'limit'>) => {
  const data = {
    ...params
  };

  // Convert time parameters to match API expectations
  if (data.timeStart) {
    data.timeStart = Number(data.timeStart);
  }
  if (data.timeEnd) {
    data.timeEnd = Number(data.timeEnd);
  }

  return apiRequest().get<{ id: number }>('/system/gift/export', {
    params: data
  });
};

// Withdraw gift order
export const withdrawGiftOrder = async (params: { id: string; note: { zh_tw: string } }) => {
  return apiRequest().put('/system/gift/withdraw', params);
};
