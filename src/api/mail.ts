import {
  InternalLetterRecord,
  InternalLetterRecordSearchParams,
  MailCategory,
  MailType
} from '@/types/mail';

import apiRequest, { IResponseDataPage } from './services';

// Get internal letter records
export const getInternalLetterRecords = async (params: InternalLetterRecordSearchParams) => {
  const data = {
    ...params
  };

  // Convert time parameters to match API expectations
  if (data.timeStart) {
    data.timeStart = Number(data.timeStart);
  }
  if (data.timeEnd) {
    data.timeEnd = Number(data.timeEnd);
  }

  return apiRequest().get<IResponseDataPage<InternalLetterRecord>>('/system/mail/record/list', {
    params: data
  });
};

// Get mail categories
export const getMailCategories = () => {
  return apiRequest().get<MailCategory[]>('/system/mail/category');
};

// Get mail types
export const getMailTypes = () => {
  return apiRequest().get<MailType[]>('/system/mail/type');
};

// Export mail records
export const triggerExportInternalLetterRecords = async (
  params: Omit<InternalLetterRecordSearchParams, 'page' | 'limit'>
) => {
  const data = {
    ...params
  };

  // Convert time parameters to match API expectations
  if (data.timeStart) {
    data.timeStart = Number(data.timeStart);
  }
  if (data.timeEnd) {
    data.timeEnd = Number(data.timeEnd);
  }

  return apiRequest().get<{ id: number }>('/system/mail/record/export', {
    params: data
  });
};

// Get mail record details
export const getMailRecordDetails = (id: number) => {
  return apiRequest().get<InternalLetterRecord>('/system/mail/record/details', {
    params: { id }
  });
};

// Batch read mail records
export const batchReadMailRecords = (ids: number[]) => {
  return apiRequest().put('/system/mail/record/list/read/batch', { ids });
};

export const withdrawnMailRecords = (id: string) => {
  return apiRequest().put('/system/mail/record/withdraw', { id, note: { zh_tw: '' } });
};
